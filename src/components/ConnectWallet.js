import { useEffect, useState } from 'react'
import { useConnectWallet } from '@web3-onboard/react'
import { ethers } from 'ethers'

export default function ConnectWallet() {
    const [{ wallet, connecting }, connect, disconnect] = useConnectWallet()
    const [ethersProvider, setProvider] = useState(null)
    const [account, setAccount] = useState(null)

    useEffect(() => {
        if (wallet?.provider) {
            const { name, avatar } = wallet?.accounts[0].ens || {}
            setAccount({
                address: wallet.accounts[0].address,
                balance: wallet.accounts[0].balance,
                ens: {
                    name: name,
                    avatar: avatar?.url
                }
            })
        }
    }, [wallet])

    useEffect(() => {
        if (wallet?.provider) {
            setProvider(new ethers.providers.Web3Provider(wallet.provider, 'any'))
            // For ethers v6:
            // setProvider(new ethers.BrowserProvider(wallet.provider, 'any'))
        }
    }, [wallet])

    if (wallet?.provider && account) {
        return (
            <div>
                {account.ens?.avatar && <img src={account.ens.avatar} alt="ENS Avatar" />}
                <div>{account.ens?.name || account.address}</div>
                <div>Connected to {wallet.label}</div>
                <button onClick={() => disconnect({ label: wallet.label })}>Disconnect</button>
            </div>
        )
    }

    return (
        <div>
            <button disabled={connecting} onClick={() => connect()}>
                Connect
            </button>
        </div>
    )
}
