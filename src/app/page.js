"use client";
import { Web3OnboardProvider, init } from '@web3-onboard/react'
import injectedModule from '@web3-onboard/injected-wallets'
import infinityWalletModule from '@web3-onboard/infinity-wallet'
import fortmaticModule from '@web3-onboard/fortmatic'
import safeModule from '@web3-onboard/gnosis'
import keepkeyModule from '@web3-onboard/keepkey'
import keystoneModule from '@web3-onboard/keystone'
import ledgerModule from '@web3-onboard/ledger'
import portisModule from '@web3-onboard/portis'
import trezorModule from '@web3-onboard/trezor'
import walletConnectModule from '@web3-onboard/walletconnect'
import coinbaseModule from '@web3-onboard/coinbase'
import magicModule from '@web3-onboard/magic'
import dcentModule from '@web3-onboard/dcent'
import sequenceModule from '@web3-onboard/sequence'
import tahoModule from '@web3-onboard/taho'
import trustModule from '@web3-onboard/trust'
import okxModule from '@web3-onboard/okx'
import frontierModule from '@web3-onboard/frontier'
import "../../public/assets/css/style.css";
import "../../public/assets/css/page.module.css";
import ConnectWallet from '@/components/ConnectWallet'

export default function Home() {
  const INFURA_KEY = ''

  const injected = injectedModule()
  const coinbase = coinbaseModule()
  const dcent = dcentModule()
  const walletConnect = walletConnectModule()

  const portis = portisModule({
    apiKey: 'apiKey'
  })

  const fortmatic = fortmaticModule({
    apiKey: 'apiKey'
  })

  const infinityWallet = infinityWalletModule()
  const ledger = ledgerModule()
  const keystone = keystoneModule()
  const keepkey = keepkeyModule()
  const safe = safeModule()
  const sequence = sequenceModule()
  const taho = tahoModule()
  const trust = trustModule()
  const okx = okxModule()
  const frontier = frontierModule()

  const trezorOptions = {
    email: '<EMAIL>',
    appUrl: 'https://www.blocknative.com'
  }

  const trezor = trezorModule(trezorOptions)

  const magic = magicModule({
    apiKey: 'apiKey'
  })

  const wallets = [
    infinityWallet,
    keepkey,
    sequence,
    injected,
    trust,
    okx,
    frontier,
    taho,
    ledger,
    coinbase,
    dcent,
    trezor,
    walletConnect,
    safe,
    magic,
    fortmatic,
    keystone,
    portis
  ]

  const chains = [
    {
      id: '0x1',
      token: 'ETH',
      label: 'Ethereum Mainnet',
      rpcUrl: `https://mainnet.infura.io/v3/${INFURA_KEY}`
    },
    {
      id: 11155111,
      token: 'ETH',
      label: 'Sepolia',
      rpcUrl: 'https://rpc.sepolia.org/'
    },
    {
      id: '0x13881',
      token: 'MATIC',
      label: 'Polygon - Mumbai',
      rpcUrl: 'https://matic-mumbai.chainstacklabs.com'
    },
    {
      id: '0x38',
      token: 'BNB',
      label: 'Binance',
      rpcUrl: 'https://bsc-dataseed.binance.org/'
    },
    {
      id: '0xA',
      token: 'OETH',
      label: 'OP Mainnet',
      rpcUrl: 'https://mainnet.optimism.io'
    },
    {
      id: '0xA4B1',
      token: 'ARB-ETH',
      label: 'Arbitrum',
      rpcUrl: 'https://rpc.ankr.com/arbitrum'
    },
    {
      id: '0xa4ec',
      token: 'ETH',
      label: 'Celo',
      rpcUrl: 'https://1rpc.io/celo'
    },
    {
      id: 666666666,
      token: 'DEGEN',
      label: 'Degen',
      rpcUrl: 'https://rpc.degen.tips'
    },
    {
      id: 2192,
      token: 'SNAX',
      label: 'SNAX Chain',
      rpcUrl: 'https://mainnet.snaxchain.io'
    }
  ]

  const appMetadata = {
    name: 'Connect Wallet Example',
    icon: '<svg>My App Icon</svg>',
    description: 'Example showcasing how to connect a wallet.',
    recommendedInjectedWallets: [
      { name: 'MetaMask', url: 'https://metamask.io' },
      { name: 'Coinbase', url: 'https://wallet.coinbase.com/' }
    ]
  }

  const web3Onboard = init({
    wallets,
    chains,
    appMetadata
  })
  return (
    <Web3OnboardProvider web3Onboard={web3Onboard}>
      <ConnectWallet />
    </Web3OnboardProvider>
  )
}
