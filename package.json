{"name": "web3-onboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@web3-onboard/coinbase": "^2.4.2", "@web3-onboard/dcent": "^2.2.10", "@web3-onboard/fortmatic": "^2.1.1", "@web3-onboard/frontier": "^2.1.1", "@web3-onboard/gnosis": "^2.3.2", "@web3-onboard/infinity-wallet": "^2.1.1", "@web3-onboard/injected-wallets": "^2.11.3", "@web3-onboard/keepkey": "^2.3.10", "@web3-onboard/keystone": "^2.3.11", "@web3-onboard/ledger": "^2.7.1", "@web3-onboard/magic": "^2.2.1", "@web3-onboard/okx": "^2.0.0", "@web3-onboard/portis": "^2.2.1", "@web3-onboard/react": "^2.11.0", "@web3-onboard/sequence": "^2.1.1", "@web3-onboard/taho": "^2.1.1", "@web3-onboard/trezor": "^2.4.7", "@web3-onboard/trust": "^2.1.2", "@web3-onboard/walletconnect": "^2.6.2", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "web3": "^4.16.0"}}